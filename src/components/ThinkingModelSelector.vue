<script setup lang="ts">
import { computed } from 'vue'
import { language } from '../stores/language'
import { getAllThinkingModelDetails, type ThinkingModel } from '../utils/systemicThinking'

interface Props {
  modelValue: ThinkingModel[]
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: ThinkingModel[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const allModels = getAllThinkingModelDetails()

const translations = computed(() => {
  if (language.value === 'zh') {
    return {
      selectModels: '选择思维模型',
      selectAtLeastOne: '请至少选择一个思维模型',
      recommended: '推荐',
      advanced: '高级'
    }
  } else {
    return {
      selectModels: 'Select Thinking Models',
      selectAtLeastOne: 'Please select at least one thinking model',
      recommended: 'Recommended',
      advanced: 'Advanced'
    }
  }
})

const toggleModel = (modelId: ThinkingModel) => {
  if (props.disabled) return
  
  const currentModels = [...props.modelValue]
  const index = currentModels.indexOf(modelId)
  
  if (index > -1) {
    currentModels.splice(index, 1)
  } else {
    currentModels.push(modelId)
  }
  
  emit('update:modelValue', currentModels)
}

const isSelected = (modelId: ThinkingModel) => {
  return props.modelValue.includes(modelId)
}

const getModelDetailedInfo = (modelId: string) => {
  const detailedInfo: { [key: string]: string } = {
    'butterfly-effect': '适用场景：分析小变化的大影响、风险评估、系统脆弱性分析。核心功能：识别关键触发点，预测连锁反应。',
    'premortem': '适用场景：项目规划、风险管理、决策前评估。核心功能：提前识别失败原因，制定预防措施。',
    'red-team': '适用场景：安全评估、策略验证、批判性思维。核心功能：从对立角度挑战假设，发现盲点。',
    'systems-thinking': '适用场景：复杂系统分析、组织管理、政策制定。核心功能：理解系统整体性，识别反馈循环。',
    'scenario-planning': '适用场景：战略规划、未来预测、应急准备。核心功能：构建多种可能情景，制定应对策略。',
    'root-cause': '适用场景：问题诊断、质量改进、事故分析。核心功能：深入挖掘根本原因，避免表面解决。'
  }
  return detailedInfo[modelId] || '暂无详细信息'
}

// 分组模型
const recommendedModels = allModels.slice(0, 3) // 前3个作为推荐
const advancedModels = allModels.slice(3) // 其余作为高级
</script>

<template>
  <div>
    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
      {{ translations.selectModels }}
    </label>
    
    <!-- 推荐模型 -->
    <div class="mb-4">
      <h4 class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-2 flex items-center">
        <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded text-xs mr-2">{{ translations.recommended }}</span>
      </h4>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
        <button
          v-for="model in recommendedModels"
          :key="model.id"
          @click="toggleModel(model.id as ThinkingModel)"
          :disabled="disabled"
          :class="[
            'p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 text-left relative',
            isSelected(model.id as ThinkingModel)
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md ring-2 ring-blue-200 dark:ring-blue-800'
              : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 hover:shadow-sm',
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          ]"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <span class="text-lg">{{ model.icon }}</span>
            </div>
            <div v-if="isSelected(model.id as ThinkingModel)" class="text-blue-500">
              <div class="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <h5 class="font-medium text-sm text-slate-800 dark:text-slate-200 mb-1">
                {{ model.name }}
              </h5>
              <p class="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                {{ model.description }}
              </p>
            </div>
            <!-- 信息图标和工具提示 -->
            <div class="relative group ml-2">
              <button
                type="button"
                class="flex items-center justify-center w-4 h-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                @click.stop
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </button>
              <!-- 详细信息工具提示 -->
              <div class="absolute right-0 top-full mt-2 w-64 p-3 bg-slate-800 dark:bg-slate-700 text-white text-xs rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div class="font-medium mb-1">{{ model.name }}</div>
                <div class="text-slate-300 dark:text-slate-400 mb-2">{{ model.description }}</div>
                <div class="text-slate-400 dark:text-slate-500 text-xs">
                  {{ getModelDetailedInfo(model.id) }}
                </div>
                <div class="absolute -top-1 right-4 w-2 h-2 bg-slate-800 dark:bg-slate-700 rotate-45"></div>
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 高级模型 -->
    <div v-if="advancedModels.length > 0">
      <h4 class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-2 flex items-center">
        <span class="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded text-xs mr-2">{{ translations.advanced }}</span>
      </h4>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
        <button
          v-for="model in advancedModels"
          :key="model.id"
          @click="toggleModel(model.id as ThinkingModel)"
          :disabled="disabled"
          :class="[
            'p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 text-left relative',
            isSelected(model.id as ThinkingModel)
              ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 shadow-md ring-2 ring-purple-200 dark:ring-purple-800'
              : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 hover:shadow-sm',
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          ]"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <span class="text-lg">{{ model.icon }}</span>
            </div>
            <div v-if="isSelected(model.id as ThinkingModel)" class="text-purple-500">
              <div class="flex items-center justify-center w-6 h-6 bg-purple-500 rounded-full">
                <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <h5 class="font-medium text-sm text-slate-800 dark:text-slate-200 mb-1">
                {{ model.name }}
              </h5>
              <p class="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                {{ model.description }}
              </p>
            </div>
            <!-- 信息图标和工具提示 -->
            <div class="relative group ml-2">
              <button
                type="button"
                class="flex items-center justify-center w-4 h-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                @click.stop
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </button>
              <!-- 详细信息工具提示 -->
              <div class="absolute right-0 top-full mt-2 w-64 p-3 bg-slate-800 dark:bg-slate-700 text-white text-xs rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div class="font-medium mb-1">{{ model.name }}</div>
                <div class="text-slate-300 dark:text-slate-400 mb-2">{{ model.description }}</div>
                <div class="text-slate-400 dark:text-slate-500 text-xs">
                  {{ getModelDetailedInfo(model.id) }}
                </div>
                <div class="absolute -top-1 right-4 w-2 h-2 bg-slate-800 dark:bg-slate-700 rotate-45"></div>
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 验证提示 -->
    <div v-if="modelValue.length === 0" class="mt-3 text-xs text-red-500 dark:text-red-400">
      {{ translations.selectAtLeastOne }}
    </div>
  </div>
</template>