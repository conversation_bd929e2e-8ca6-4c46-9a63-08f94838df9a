<script setup lang="ts">
import { computed } from 'vue'
import { language } from '../stores/language'
import { getAllThinkingModelDetails, type ThinkingModel } from '../utils/systemicThinking'

interface Props {
  modelValue: ThinkingModel[]
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: ThinkingModel[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const allModels = getAllThinkingModelDetails()

const translations = computed(() => {
  if (language.value === 'zh') {
    return {
      selectModels: '选择思维模型',
      selectAtLeastOne: '请至少选择一个思维模型',
      recommended: '推荐',
      advanced: '高级'
    }
  } else {
    return {
      selectModels: 'Select Thinking Models',
      selectAtLeastOne: 'Please select at least one thinking model',
      recommended: 'Recommended',
      advanced: 'Advanced'
    }
  }
})

const toggleModel = (modelId: ThinkingModel) => {
  if (props.disabled) return
  
  const currentModels = [...props.modelValue]
  const index = currentModels.indexOf(modelId)
  
  if (index > -1) {
    currentModels.splice(index, 1)
  } else {
    currentModels.push(modelId)
  }
  
  emit('update:modelValue', currentModels)
}

const isSelected = (modelId: ThinkingModel) => {
  return props.modelValue.includes(modelId)
}

// 分组模型
const recommendedModels = allModels.slice(0, 3) // 前3个作为推荐
const advancedModels = allModels.slice(3) // 其余作为高级
</script>

<template>
  <div>
    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
      {{ translations.selectModels }}
    </label>
    
    <!-- 推荐模型 -->
    <div class="mb-4">
      <h4 class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-2 flex items-center">
        <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded text-xs mr-2">{{ translations.recommended }}</span>
      </h4>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
        <button
          v-for="model in recommendedModels"
          :key="model.id"
          @click="toggleModel(model.id as ThinkingModel)"
          :disabled="disabled"
          :class="[
            'p-3 sm:p-4 rounded border-2 transition-colors text-left',
            isSelected(model.id as ThinkingModel)
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500',
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          ]"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <span class="text-lg">{{ model.icon }}</span>
              <div 
                class="w-3 h-3 rounded-full" 
                :style="{ backgroundColor: model.color }"
              ></div>
            </div>
            <div v-if="isSelected(model.id as ThinkingModel)" class="text-blue-500">
              <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
          <h5 class="font-medium text-sm text-slate-800 dark:text-slate-200 mb-1">
            {{ model.name }}
          </h5>
          <p class="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
            {{ model.description }}
          </p>
        </button>
      </div>
    </div>

    <!-- 高级模型 -->
    <div v-if="advancedModels.length > 0">
      <h4 class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-2 flex items-center">
        <span class="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded text-xs mr-2">{{ translations.advanced }}</span>
      </h4>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
        <button
          v-for="model in advancedModels"
          :key="model.id"
          @click="toggleModel(model.id as ThinkingModel)"
          :disabled="disabled"
          :class="[
            'p-3 sm:p-4 rounded border-2 transition-colors text-left',
            isSelected(model.id as ThinkingModel)
              ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
              : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500',
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          ]"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <span class="text-lg">{{ model.icon }}</span>
              <div 
                class="w-3 h-3 rounded-full" 
                :style="{ backgroundColor: model.color }"
              ></div>
            </div>
            <div v-if="isSelected(model.id as ThinkingModel)" class="text-purple-500">
              <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
          <h5 class="font-medium text-sm text-slate-800 dark:text-slate-200 mb-1">
            {{ model.name }}
          </h5>
          <p class="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
            {{ model.description }}
          </p>
        </button>
      </div>
    </div>

    <!-- 验证提示 -->
    <div v-if="modelValue.length === 0" class="mt-3 text-xs text-red-500 dark:text-red-400">
      {{ translations.selectAtLeastOne }}
    </div>
  </div>
</template>